using System.Net;
using AssistantService.Services;
using AssistantService.Configuration;
using AssistantService.Data;
using Microsoft.AspNetCore.Hosting;
using Microsoft.AspNetCore.Mvc.Testing;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Configuration;
using Microsoft.EntityFrameworkCore;
using Moq;

namespace AssistantService.Tests.TestHelpers;

public class TestWebApplicationFactory : WebApplicationFactory<Program>
{
    public Mock<IAIClient> MockAIClient { get; private set; } = null!;

    protected override void ConfigureWebHost(IWebHostBuilder builder)
    {
        builder.ConfigureServices(services =>
        {
            // Remove the existing IAIClient registration
            var aiClientDescriptor = services.SingleOrDefault(d => d.ServiceType == typeof(IAIClient));
            if (aiClientDescriptor != null)
                services.Remove(aiClientDescriptor);

            // Create a mock IAIClient directly instead of trying to mock HttpClient
            MockAIClient = new Mock<IAIClient>();

            // Set up default mock response for integration tests
            MockAIClient
                .Setup(x => x.AskAsync(It.IsAny<string>(), It.IsAny<AssistantService.Services.AIProvider>()))
                .ReturnsAsync("Mocked response from Ollama");

    services.AddSingleton(MockAIClient.Object);

            // Remove existing database context registration (if any)
            var dbContextDescriptor = services.SingleOrDefault(d => d.ServiceType == typeof(DbContextOptions<AssistantDbContext>));
            if (dbContextDescriptor != null)
                services.Remove(dbContextDescriptor);

            var contextDescriptor = services.SingleOrDefault(d => d.ServiceType == typeof(AssistantDbContext));
            if (contextDescriptor != null)
                services.Remove(contextDescriptor);

            // Add in-memory database for testing
            services.AddDbContext<AssistantDbContext>(options =>
            {
                options.UseInMemoryDatabase("TestDb");
            });
        });

        // Configure test settings to disable security features
        builder.ConfigureAppConfiguration((context, config) =>
        {
            config.AddInMemoryCollection(new Dictionary<string, string?>
            {
                ["Security:EnableRateLimiting"] = "false",
                ["Security:EnablePromptInjectionDetection"] = "false",
                ["Security:EnableContentFiltering"] = "false"
            });
        });

        builder.UseEnvironment("Testing");
    }

    public void SetupMockResponse(string response, HttpStatusCode statusCode = HttpStatusCode.OK)
    {
        if (MockAIClient == null)
            throw new InvalidOperationException("MockAIClient is not initialized");

        MockAIClient
            .Setup(x => x.AskAsync(It.IsAny<string>(), It.IsAny<AssistantService.Services.AIProvider>()))
            .ReturnsAsync(response);
    }

    public void SetupMockException(Exception exception)
    {
        if (MockAIClient == null)
            throw new InvalidOperationException("MockAIClient is not initialized");

        MockAIClient
            .Setup(x => x.AskAsync(It.IsAny<string>(), It.IsAny<AssistantService.Services.AIProvider>()))
            .ThrowsAsync(exception);
    }

    public void VerifyOllamaCall(Times times)
    {
        if (MockAIClient == null)
            throw new InvalidOperationException("MockAIClient is not initialized");

        MockAIClient
            .Verify(x => x.AskAsync(It.IsAny<string>(), It.IsAny<AssistantService.Services.AIProvider>()), times);
    }

    public void ResetMock()
    {
        MockAIClient?.Reset();
    }
}
