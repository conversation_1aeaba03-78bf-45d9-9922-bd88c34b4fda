using AssistantService.Services;
using AssistantService.Configuration;
using AssistantService.Data;
using Microsoft.AspNetCore.Hosting;
using Microsoft.AspNetCore.Mvc.Testing;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Configuration;
using Microsoft.EntityFrameworkCore;
using Microsoft.AspNetCore.Authentication;
using Microsoft.AspNetCore.Authorization;
using System.Security.Claims;
using System.Text.Encodings.Web;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Moq;

namespace AssistantService.Tests.TestHelpers;

public class TestAuthenticationHandler : AuthenticationHandler<AuthenticationSchemeOptions>
{
    public TestAuthenticationHandler(IOptionsMonitor<AuthenticationSchemeOptions> options,
        ILoggerFactory logger, UrlEncoder encoder, ISystemClock clock)
        : base(options, logger, encoder, clock)
    {
    }

    protected override Task<AuthenticateResult> HandleAuthenticateAsync()
    {
        var claims = new[]
        {
            new Claim(ClaimTypes.Name, "Test User"),
            new Claim(ClaimTypes.NameIdentifier, "00000000-0000-0000-0000-000000000001"),
            new Claim("sub", "00000000-0000-0000-0000-000000000001")
        };

        var identity = new ClaimsIdentity(claims, "Test");
        var principal = new ClaimsPrincipal(identity);
        var ticket = new AuthenticationTicket(principal, "Test");

        return Task.FromResult(AuthenticateResult.Success(ticket));
    }
}

public class TestWebApplicationFactory : WebApplicationFactory<Program>
{
    public Mock<IAIClient> MockAIClient { get; private set; } = null!;

    protected override void ConfigureWebHost(IWebHostBuilder builder)
    {
        builder.ConfigureServices(services =>
        {
            // Remove the existing IAIClient registration
            var aiClientDescriptor = services.SingleOrDefault(d => d.ServiceType == typeof(IAIClient));
            if (aiClientDescriptor != null)
                services.Remove(aiClientDescriptor);

            // Create a mock IAIClient directly instead of trying to mock HttpClient
            MockAIClient = new Mock<IAIClient>();

            // Set up default mock response for integration tests
            MockAIClient
                .Setup(x => x.AskAsync(It.IsAny<string>(), It.IsAny<AIProvider>()))
                .ReturnsAsync("Mocked response from Ollama");

            services.AddSingleton(MockAIClient.Object);

            // Remove existing database context registration (if any)
            var dbContextDescriptor = services.SingleOrDefault(d => d.ServiceType == typeof(DbContextOptions<AssistantDbContext>));
            if (dbContextDescriptor != null)
                services.Remove(dbContextDescriptor);

            var contextDescriptor = services.SingleOrDefault(d => d.ServiceType == typeof(AssistantDbContext));
            if (contextDescriptor != null)
                services.Remove(contextDescriptor);

            // Add in-memory database for testing
            services.AddDbContext<AssistantDbContext>(options =>
            {
                options.UseInMemoryDatabase("TestDb");
            });

            // Configure test authentication
            services.AddAuthentication("Test")
                .AddScheme<AuthenticationSchemeOptions, TestAuthenticationHandler>("Test", options => { });

            services.AddAuthorization(options =>
            {
                options.DefaultPolicy = new AuthorizationPolicyBuilder("Test")
                    .RequireAuthenticatedUser()
                    .Build();
            });
        });

        // Configure test settings to disable security features
        builder.ConfigureAppConfiguration((context, config) =>
        {
            config.AddInMemoryCollection(new Dictionary<string, string?>
            {
                ["Security:EnableRateLimiting"] = "false",
                ["Security:EnablePromptInjectionDetection"] = "false",
                ["Security:EnableContentFiltering"] = "false"
            });
        });

        builder.UseEnvironment("Testing");
    }

    public void SetupMockResponse(string response)
    {
        if (MockAIClient == null)
            throw new InvalidOperationException("MockAIClient is not initialized");

        MockAIClient
            .Setup(x => x.AskAsync(It.IsAny<string>(), It.IsAny<AIProvider>()))
            .ReturnsAsync(response);
    }

    public void SetupMockException(Exception exception)
    {
        if (MockAIClient == null)
            throw new InvalidOperationException("MockAIClient is not initialized");

        MockAIClient
            .Setup(x => x.AskAsync(It.IsAny<string>(), It.IsAny<AIProvider>()))
            .ThrowsAsync(exception);
    }

    public void VerifyOllamaCall(Times times)
    {
        if (MockAIClient == null)
            throw new InvalidOperationException("MockAIClient is not initialized");

        MockAIClient
            .Verify(x => x.AskAsync(It.IsAny<string>(), It.IsAny<AIProvider>()), times);
    }

    public void ResetMock()
    {
        MockAIClient?.Reset();
    }
}
